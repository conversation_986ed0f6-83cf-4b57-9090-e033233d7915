* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #2c3e50;
    color: #ecf0f1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.game-container {
    display: flex;
    gap: 20px;
    background-color: #34495e;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.ui-panel {
    width: 200px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.stats {
    background-color: #2c3e50;
    padding: 15px;
    border-radius: 8px;
}

.stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 16px;
}

.stat:last-child {
    margin-bottom: 0;
}

.label {
    font-weight: bold;
}

.controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

button {
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}

#start-wave {
    background-color: #27ae60;
    color: white;
}

#start-wave:hover {
    background-color: #2ecc71;
}

#start-wave:disabled {
    background-color: #7f8c8d;
    cursor: not-allowed;
}

.tower-btn {
    background-color: #3498db;
    color: white;
}

.tower-btn:hover {
    background-color: #5dade2;
}

.tower-btn:disabled {
    background-color: #7f8c8d;
    cursor: not-allowed;
}

.tower-btn.active {
    background-color: #e74c3c;
}

.info {
    background-color: #2c3e50;
    padding: 15px;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.4;
}

.game-area {
    position: relative;
}

#gameCanvas {
    border: 2px solid #7f8c8d;
    border-radius: 8px;
    background-color: #27ae60;
    cursor: crosshair;
}

#gameCanvas.placing-tower {
    cursor: crosshair;
}
