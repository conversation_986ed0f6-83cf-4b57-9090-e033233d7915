// Game class
class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.towers = [];
        this.enemies = [];
        this.projectiles = [];
        this.gold = 100;
        this.lives = 20;
        this.currentWave = 1;
        this.wave = null;
        this.gameRunning = false;
        this.placingTower = false;
        
        // Define path
        this.path = this.createPath();
        
        // Bind event handlers
        this.canvas.addEventListener('click', this.handleCanvasClick.bind(this));
        document.getElementById('start-wave').addEventListener('click', this.startWave.bind(this));
        document.getElementById('place-tower').addEventListener('click', this.toggleTowerPlacement.bind(this));
        
        // Start game loop
        this.gameLoop();
    }

    createPath() {
        const path = [];
        // Create a simple path from left to right with some turns
        const waypoints = [
            {x: 0, y: 7},      // Start left side
            {x: 4, y: 7},      // Move right
            {x: 4, y: 3},      // Turn up
            {x: 8, y: 3},      // Move right
            {x: 8, y: 10},     // Turn down
            {x: 12, y: 10},    // Move right
            {x: 12, y: 5},     // Turn up
            {x: 16, y: 5},     // Move right
            {x: 16, y: 8},     // Turn down
            {x: 20, y: 8}      // End right side
        ];

        // Convert grid positions to world positions and interpolate
        for (let i = 0; i < waypoints.length - 1; i++) {
            const start = getWorldPosition(waypoints[i].x, waypoints[i].y);
            const end = getWorldPosition(waypoints[i + 1].x, waypoints[i + 1].y);
            
            path.push(start);
            
            // Add intermediate points for smooth movement
            const steps = Math.max(Math.abs(waypoints[i + 1].x - waypoints[i].x), 
                                 Math.abs(waypoints[i + 1].y - waypoints[i].y));
            
            for (let step = 1; step < steps; step++) {
                const t = step / steps;
                path.push({
                    x: start.x + (end.x - start.x) * t,
                    y: start.y + (end.y - start.y) * t
                });
            }
        }
        
        path.push(getWorldPosition(waypoints[waypoints.length - 1].x, waypoints[waypoints.length - 1].y));
        return path;
    }

    startWave() {
        if (this.wave && !this.wave.isComplete()) return;
        
        this.wave = new Wave(this.currentWave);
        this.wave.start(this.path);
        this.gameRunning = true;
        
        document.getElementById('start-wave').disabled = true;
        this.updateUI();
    }

    toggleTowerPlacement() {
        this.placingTower = !this.placingTower;
        const btn = document.getElementById('place-tower');
        btn.classList.toggle('active', this.placingTower);
        btn.textContent = this.placingTower ? 'Cancel' : `Place Tower ($${CONFIG.TOWER_COST})`;
        this.canvas.classList.toggle('placing-tower', this.placingTower);
    }

    handleCanvasClick(event) {
        if (!this.placingTower) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        const gridPos = getGridPosition(x, y);
        
        // Check if position is valid and affordable
        if (this.canPlaceTower(gridPos.x, gridPos.y) && this.gold >= CONFIG.TOWER_COST) {
            this.towers.push(new Tower(gridPos.x, gridPos.y));
            this.gold -= CONFIG.TOWER_COST;
            this.toggleTowerPlacement();
            this.updateUI();
        }
    }

    canPlaceTower(gridX, gridY) {
        // Check bounds
        if (gridX < 0 || gridX >= 20 || gridY < 0 || gridY >= 15) return false;
        
        // Check if tower already exists
        for (let tower of this.towers) {
            if (tower.gridX === gridX && tower.gridY === gridY) return false;
        }
        
        // Check if on path (simplified - just check if too close to path)
        const worldPos = getWorldPosition(gridX, gridY);
        for (let pathPoint of this.path) {
            if (distance(worldPos, pathPoint) < CONFIG.GRID_SIZE * 0.7) return false;
        }
        
        return true;
    }

    update() {
        const currentTime = Date.now();
        
        // Update wave
        if (this.wave) {
            this.wave.update(currentTime);
            this.enemies = this.wave.enemies;
        }
        
        // Update enemies
        for (let enemy of this.enemies) {
            enemy.update();
            
            // Check if enemy reached end
            if (enemy.alive && enemy.hasReachedEnd()) {
                this.lives--;
                enemy.alive = false;
            }
        }
        
        // Remove dead enemies and award gold
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            if (!this.enemies[i].alive) {
                if (this.enemies[i].health <= 0) {
                    this.gold += CONFIG.ENEMY_REWARD;
                }
                this.enemies.splice(i, 1);
            }
        }
        
        // Update towers
        for (let tower of this.towers) {
            tower.update(this.enemies, this.projectiles, currentTime);
        }
        
        // Update projectiles
        for (let i = this.projectiles.length - 1; i >= 0; i--) {
            this.projectiles[i].update();
            if (!this.projectiles[i].alive) {
                this.projectiles.splice(i, 1);
            }
        }
        
        // Check wave completion
        if (this.wave && this.wave.isComplete()) {
            this.currentWave++;
            this.wave = null;
            document.getElementById('start-wave').disabled = false;
        }
        
        // Check game over
        if (this.lives <= 0) {
            this.gameRunning = false;
            alert('Game Over!');
        }
        
        this.updateUI();
    }

    draw() {
        // Clear canvas
        this.ctx.fillStyle = '#27ae60';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw grid
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        for (let x = 0; x <= this.canvas.width; x += CONFIG.GRID_SIZE) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        for (let y = 0; y <= this.canvas.height; y += CONFIG.GRID_SIZE) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
        
        // Draw path
        this.ctx.strokeStyle = '#8b4513';
        this.ctx.lineWidth = 20;
        this.ctx.beginPath();
        this.ctx.moveTo(this.path[0].x, this.path[0].y);
        for (let i = 1; i < this.path.length; i++) {
            this.ctx.lineTo(this.path[i].x, this.path[i].y);
        }
        this.ctx.stroke();
        
        // Draw towers
        for (let tower of this.towers) {
            tower.draw(this.ctx);
        }
        
        // Draw enemies
        for (let enemy of this.enemies) {
            enemy.draw(this.ctx);
        }
        
        // Draw projectiles
        for (let projectile of this.projectiles) {
            projectile.draw(this.ctx);
        }
    }

    updateUI() {
        document.getElementById('gold').textContent = this.gold;
        document.getElementById('lives').textContent = this.lives;
        document.getElementById('wave').textContent = this.currentWave;
        
        const towerBtn = document.getElementById('place-tower');
        towerBtn.disabled = this.gold < CONFIG.TOWER_COST;
        
        const status = document.getElementById('game-status');
        if (this.lives <= 0) {
            status.textContent = 'Game Over!';
        } else if (this.wave && !this.wave.isComplete()) {
            status.textContent = `Wave ${this.currentWave} in progress...`;
        } else {
            status.textContent = `Ready for wave ${this.currentWave}`;
        }
    }

    gameLoop() {
        this.update();
        this.draw();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    new Game();
});
