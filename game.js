// Game Configuration
const CONFIG = {
    GRID_SIZE: 40,
    CANVAS_WIDTH: 800,
    CANVAS_HEIGHT: 600,
    TOWER_COST: 50,
    TOWER_RANGE: 80,
    TOWER_DAMAGE: 25,
    ENEMY_SPEED: 1,
    ENEMY_HEALTH: 100,
    ENEMY_REWARD: 10
};

// Utility functions
function distance(a, b) {
    return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2);
}

function getGridPosition(x, y) {
    return {
        x: Math.floor(x / CONFIG.GRID_SIZE),
        y: Math.floor(y / CONFIG.GRID_SIZE)
    };
}

function getWorldPosition(gridX, gridY) {
    return {
        x: gridX * CONFIG.GRID_SIZE + CONFIG.GRID_SIZE / 2,
        y: gridY * CONFIG.GRID_SIZE + CONFIG.GRID_SIZE / 2
    };
}

// Enemy class
class Enemy {
    constructor(path) {
        this.path = path;
        this.pathIndex = 0;
        this.position = { ...path[0] };
        this.health = CONFIG.ENEMY_HEALTH;
        this.maxHealth = CONFIG.ENEMY_HEALTH;
        this.speed = CONFIG.ENEMY_SPEED;
        this.radius = 12;
        this.alive = true;
    }

    update() {
        if (!this.alive || this.pathIndex >= this.path.length - 1) return;

        const target = this.path[this.pathIndex + 1];
        const dx = target.x - this.position.x;
        const dy = target.y - this.position.y;
        const dist = Math.sqrt(dx * dx + dy * dy);

        if (dist < this.speed) {
            this.pathIndex++;
            if (this.pathIndex < this.path.length) {
                this.position = { ...this.path[this.pathIndex] };
            }
        } else {
            this.position.x += (dx / dist) * this.speed;
            this.position.y += (dy / dist) * this.speed;
        }
    }

    takeDamage(damage) {
        this.health -= damage;
        if (this.health <= 0) {
            this.alive = false;
        }
    }

    draw(ctx) {
        if (!this.alive) return;

        // Draw enemy body
        ctx.fillStyle = '#e74c3c';
        ctx.beginPath();
        ctx.arc(this.position.x, this.position.y, this.radius, 0, Math.PI * 2);
        ctx.fill();

        // Draw health bar
        const barWidth = 20;
        const barHeight = 4;
        const healthPercent = this.health / this.maxHealth;
        
        ctx.fillStyle = '#2c3e50';
        ctx.fillRect(this.position.x - barWidth/2, this.position.y - this.radius - 8, barWidth, barHeight);
        
        ctx.fillStyle = healthPercent > 0.5 ? '#27ae60' : healthPercent > 0.25 ? '#f39c12' : '#e74c3c';
        ctx.fillRect(this.position.x - barWidth/2, this.position.y - this.radius - 8, barWidth * healthPercent, barHeight);
    }

    hasReachedEnd() {
        return this.pathIndex >= this.path.length - 1;
    }
}

// Tower class
class Tower {
    constructor(x, y) {
        this.position = getWorldPosition(x, y);
        this.gridX = x;
        this.gridY = y;
        this.range = CONFIG.TOWER_RANGE;
        this.damage = CONFIG.TOWER_DAMAGE;
        this.lastShot = 0;
        this.shootCooldown = 1000; // ms
        this.target = null;
    }

    update(enemies, projectiles, currentTime) {
        // Find target
        this.target = null;
        let closestDistance = this.range;
        
        for (let enemy of enemies) {
            if (!enemy.alive) continue;
            
            const dist = distance(this.position, enemy.position);
            if (dist <= this.range && dist < closestDistance) {
                this.target = enemy;
                closestDistance = dist;
            }
        }

        // Shoot at target
        if (this.target && currentTime - this.lastShot > this.shootCooldown) {
            projectiles.push(new Projectile(
                this.position.x,
                this.position.y,
                this.target,
                this.damage
            ));
            this.lastShot = currentTime;
        }
    }

    draw(ctx) {
        // Draw tower base
        ctx.fillStyle = '#34495e';
        ctx.fillRect(
            this.position.x - CONFIG.GRID_SIZE/2,
            this.position.y - CONFIG.GRID_SIZE/2,
            CONFIG.GRID_SIZE,
            CONFIG.GRID_SIZE
        );

        // Draw tower
        ctx.fillStyle = '#3498db';
        ctx.beginPath();
        ctx.arc(this.position.x, this.position.y, 15, 0, Math.PI * 2);
        ctx.fill();

        // Draw range (when selected)
        if (this.showRange) {
            ctx.strokeStyle = 'rgba(52, 152, 219, 0.3)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(this.position.x, this.position.y, this.range, 0, Math.PI * 2);
            ctx.stroke();
        }
    }
}

// Projectile class
class Projectile {
    constructor(x, y, target, damage) {
        this.position = { x, y };
        this.target = target;
        this.damage = damage;
        this.speed = 5;
        this.alive = true;
    }

    update() {
        if (!this.alive || !this.target.alive) {
            this.alive = false;
            return;
        }

        const dx = this.target.position.x - this.position.x;
        const dy = this.target.position.y - this.position.y;
        const dist = Math.sqrt(dx * dx + dy * dy);

        if (dist < this.speed) {
            // Hit target
            this.target.takeDamage(this.damage);
            this.alive = false;
        } else {
            this.position.x += (dx / dist) * this.speed;
            this.position.y += (dy / dist) * this.speed;
        }
    }

    draw(ctx) {
        if (!this.alive) return;
        
        ctx.fillStyle = '#f1c40f';
        ctx.beginPath();
        ctx.arc(this.position.x, this.position.y, 3, 0, Math.PI * 2);
        ctx.fill();
    }
}
